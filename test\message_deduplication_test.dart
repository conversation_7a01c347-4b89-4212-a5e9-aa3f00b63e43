import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:uuid/uuid.dart';
import 'package:vector/domain/repositories/message_repository.dart';
import 'package:vector/domain/models/message_model.dart';
import 'package:vector/domain/models/message_status_model.dart';
import 'package:vector/domain/models/chat_room_model.dart';
import 'package:vector/domain/models/user_model.dart';
import 'package:vector/presentation/blocs/messages/message_bloc.dart';
import 'package:vector/presentation/blocs/messages/message_event.dart';
import 'package:vector/presentation/blocs/messages/message_state.dart';

// Generate mocks
@GenerateMocks([MessageRepository])
import 'message_deduplication_test.mocks.dart';

void main() {
  group('Message Deduplication Tests', () {
    late MessageBloc messageBloc;
    late MockMessageRepository mockRepository;
    late ChatRoomModel testChatRoom;
    late UserModel testUser;
    const uuid = Uuid();

    setUp(() {
      mockRepository = MockMessageRepository();

      // Setup mock streams BEFORE creating MessageBloc
      when(
        mockRepository.getMessageStream(),
      ).thenAnswer((_) => Stream<MessageModel>.empty());
      when(
        mockRepository.getMessageStatusStream(),
      ).thenAnswer((_) => Stream<MessageStatusModel>.empty());

      messageBloc = MessageBloc(mockRepository);

      // Create test data
      testUser = UserModel(
        id: 1,
        username: 'testuser',
        email: '<EMAIL>',
        fullName: 'Test User',
        profilePicture: null,
        isOnline: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      testChatRoom = ChatRoomModel(
        id: '1',
        name: 'Test Room',
        type: ChatRoomType.private,
        participants: [testUser],
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        lastMessage: null,
        lastMessageTime: null,
        lastMessageSender: null,
        unreadCount: 0,
      );
    });

    tearDown(() {
      messageBloc.close();
    });

    test('should generate unique client IDs for messages', () {
      final clientId1 = uuid.v4();
      final clientId2 = uuid.v4();

      expect(clientId1, isNot(equals(clientId2)));
      expect(clientId1.length, equals(36)); // UUID v4 length
      expect(clientId2.length, equals(36));
    });

    test('should prevent duplicate messages with same client ID', () async {
      // Setup mock to return a message
      final testMessage = MessageModel(
        id: 'msg1',
        content: 'Test message',
        type: MessageContentType.text,
        sender: testUser,
        chatRoom: testChatRoom,
        sentAt: DateTime.now(),
        status: MessageStatus.sent,
        metadata: {'clientId': 'client123'},
      );

      when(
        mockRepository.sendMessage(
          any,
          any,
          any,
          clientId: anyNamed('clientId'),
        ),
      ).thenAnswer((_) async => testMessage);

      // Load messages first to set up the state
      when(
        mockRepository.getChatRoomMessages(
          any,
          page: anyNamed('page'),
          size: anyNamed('size'),
        ),
      ).thenAnswer((_) async => []);

      messageBloc.add(LoadMessages(chatRoomId: '1'));
      await Future.delayed(Duration(milliseconds: 100));

      // Ensure we're in MessagesLoaded state
      expect(messageBloc.state, isA<MessagesLoaded>());

      // Send message with specific client ID
      messageBloc.add(
        SendMessage(
          chatRoomId: '1',
          content: 'Test message',
          contentType: MessageContentType.text,
          clientId: 'client123',
        ),
      );

      await Future.delayed(Duration(milliseconds: 200));

      // At this point, the message should be in the MessagesLoaded state
      var state = messageBloc.state;
      expect(state, isA<MessagesLoaded>());
      var loadedState = state as MessagesLoaded;
      expect(loadedState.messages.length, equals(1));

      // Try to receive the same message via WebSocket (simulating server echo)
      messageBloc.add(MessageReceived(message: testMessage));

      await Future.delayed(Duration(milliseconds: 200));

      // Verify the message still appears only once (duplicate was prevented)
      state = messageBloc.state;
      expect(state, isA<MessagesLoaded>());
      loadedState = state as MessagesLoaded;
      expect(loadedState.messages.length, equals(1));
      expect(loadedState.messages.first.id, equals('msg1'));
    });

    test('should prevent duplicate messages with same server ID', () async {
      // Setup mock to return messages
      final testMessage = MessageModel(
        id: 'msg1',
        content: 'Test message',
        type: MessageContentType.text,
        sender: testUser,
        chatRoom: testChatRoom,
        sentAt: DateTime.now(),
        status: MessageStatus.sent,
      );

      when(
        mockRepository.getChatRoomMessages(
          any,
          page: anyNamed('page'),
          size: anyNamed('size'),
        ),
      ).thenAnswer((_) async => []);

      // Load messages first
      messageBloc.add(LoadMessages(chatRoomId: '1'));
      await Future.delayed(Duration(milliseconds: 100));

      // Receive message via WebSocket
      messageBloc.add(MessageReceived(message: testMessage));
      await Future.delayed(Duration(milliseconds: 100));

      // Try to receive the same message again
      messageBloc.add(MessageReceived(message: testMessage));
      await Future.delayed(Duration(milliseconds: 100));

      // Verify the message appears only once
      final state = messageBloc.state;
      expect(state, isA<MessagesLoaded>());
      final loadedState = state as MessagesLoaded;
      expect(loadedState.messages.length, equals(1));
      expect(loadedState.messages.first.id, equals('msg1'));
    });

    test('should allow different messages with different IDs', () async {
      // Setup mock to return messages
      final testMessage1 = MessageModel(
        id: 'msg1',
        content: 'Test message 1',
        type: MessageContentType.text,
        sender: testUser,
        chatRoom: testChatRoom,
        sentAt: DateTime.now(),
        status: MessageStatus.sent,
      );

      final testMessage2 = MessageModel(
        id: 'msg2',
        content: 'Test message 2',
        type: MessageContentType.text,
        sender: testUser,
        chatRoom: testChatRoom,
        sentAt: DateTime.now().add(Duration(seconds: 1)),
        status: MessageStatus.sent,
      );

      when(
        mockRepository.getChatRoomMessages(
          any,
          page: anyNamed('page'),
          size: anyNamed('size'),
        ),
      ).thenAnswer((_) async => []);

      // Load messages first
      messageBloc.add(LoadMessages(chatRoomId: '1'));
      await Future.delayed(Duration(milliseconds: 100));

      // Receive first message
      messageBloc.add(MessageReceived(message: testMessage1));
      await Future.delayed(Duration(milliseconds: 100));

      // Receive second message
      messageBloc.add(MessageReceived(message: testMessage2));
      await Future.delayed(Duration(milliseconds: 100));

      // Verify both messages are present
      final state = messageBloc.state;
      expect(state, isA<MessagesLoaded>());
      final loadedState = state as MessagesLoaded;
      expect(loadedState.messages.length, equals(2));
      expect(
        loadedState.messages.map((m) => m.id),
        containsAll(['msg1', 'msg2']),
      );
    });

    test(
      'should clear processed message IDs when loading new chat room',
      () async {
        when(
          mockRepository.getChatRoomMessages(
            any,
            page: anyNamed('page'),
            size: anyNamed('size'),
          ),
        ).thenAnswer((_) async => []);

        // Load messages for room 1
        messageBloc.add(LoadMessages(chatRoomId: '1'));
        await Future.delayed(Duration(milliseconds: 100));

        // Add a message to room 1
        final testMessage1 = MessageModel(
          id: 'msg1',
          content: 'Test message 1',
          type: MessageContentType.text,
          sender: testUser,
          chatRoom: testChatRoom,
          sentAt: DateTime.now(),
          status: MessageStatus.sent,
        );

        messageBloc.add(MessageReceived(message: testMessage1));
        await Future.delayed(Duration(milliseconds: 100));

        // Load messages for room 2 (should clear processed IDs for room 1)
        messageBloc.add(LoadMessages(chatRoomId: '2'));
        await Future.delayed(Duration(milliseconds: 100));

        // Switch back to room 1 and try to add the same message again
        messageBloc.add(LoadMessages(chatRoomId: '1'));
        await Future.delayed(Duration(milliseconds: 100));

        messageBloc.add(MessageReceived(message: testMessage1));
        await Future.delayed(Duration(milliseconds: 100));

        // The message should be added again since processed IDs were cleared
        final state = messageBloc.state;
        expect(state, isA<MessagesLoaded>());
        final loadedState = state as MessagesLoaded;
        expect(loadedState.messages.length, equals(1));
        expect(loadedState.messages.first.id, equals('msg1'));
      },
    );
  });
}
