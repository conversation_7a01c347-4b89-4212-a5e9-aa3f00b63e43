import 'package:flutter_test/flutter_test.dart';

void main() {
  group('Duplicate Message Fix Logic Tests', () {
    test('should create unique message keys for duplicate detection', () {
      // Test the message key generation logic
      const roomId = '123';
      const messageText = 'Hello World';
      
      // Simulate the key generation from ChatProvider
      final timestamp1 = DateTime.now().millisecondsSinceEpoch ~/ 1000;
      final messageKey1 = '${roomId}_${messageText}_$timestamp1';
      
      // Same message in the same second should have the same key
      final messageKey2 = '${roomId}_${messageText}_$timestamp1';
      
      expect(messageKey1, equals(messageKey2));
      
      // Different message should have different key
      const differentText = 'Different message';
      final differentKey = '${roomId}_${differentText}_$timestamp1';
      
      expect(messageKey1, isNot(equals(differentKey)));
    });

    test('should handle subscription tracking correctly', () {
      // Test the subscription tracking logic
      final activeSubscriptions = <String>{};
      const roomId = '123';
      
      // First subscription
      expect(activeSubscriptions.contains(roomId), isFalse);
      activeSubscriptions.add(roomId);
      expect(activeSubscriptions.contains(roomId), isTrue);
      
      // Attempt to add same subscription again
      final sizeBefore = activeSubscriptions.length;
      activeSubscriptions.add(roomId); // Set won't add duplicates
      expect(activeSubscriptions.length, equals(sizeBefore));
      
      // Remove subscription
      activeSubscriptions.remove(roomId);
      expect(activeSubscriptions.contains(roomId), isFalse);
    });

    test('should handle message deduplication logic', () {
      // Test the message deduplication logic
      final recentlySentMessages = <String, Set<String>>{};
      const roomId = '123';
      const messageText = 'Hello World';
      
      final timestamp = DateTime.now().millisecondsSinceEpoch ~/ 1000;
      final messageKey = '${roomId}_${messageText}_$timestamp';
      
      // First message should be allowed
      expect(recentlySentMessages.containsKey(roomId), isFalse);
      
      // Track the message
      recentlySentMessages[roomId] = <String>{};
      recentlySentMessages[roomId]!.add(messageKey);
      
      // Same message should be detected as duplicate
      expect(recentlySentMessages[roomId]!.contains(messageKey), isTrue);
      
      // Different message should be allowed
      const differentText = 'Different message';
      final differentKey = '${roomId}_${differentText}_$timestamp';
      expect(recentlySentMessages[roomId]!.contains(differentKey), isFalse);
    });

    test('should handle cleanup of old message keys', () {
      // Test the cleanup logic for old message keys
      final recentlySentMessages = <String, Set<String>>{};
      const roomId = '123';
      
      recentlySentMessages[roomId] = <String>{};
      
      // Add 15 messages (more than the limit of 10)
      for (int i = 0; i < 15; i++) {
        final messageKey = '${roomId}_message_$i';
        recentlySentMessages[roomId]!.add(messageKey);
      }
      
      expect(recentlySentMessages[roomId]!.length, equals(15));
      
      // Simulate cleanup (keep only last 10)
      if (recentlySentMessages[roomId]!.length > 10) {
        final oldKeys = recentlySentMessages[roomId]!.take(
          recentlySentMessages[roomId]!.length - 10
        ).toList();
        recentlySentMessages[roomId]!.removeAll(oldKeys);
      }
      
      expect(recentlySentMessages[roomId]!.length, equals(10));
    });

    test('should handle STOMP subscription tracking', () {
      // Test STOMP subscription tracking logic
      final activeStompSubscriptions = <String>{};
      const roomId = '123';
      
      // Check if already subscribed (should be false initially)
      expect(activeStompSubscriptions.contains(roomId), isFalse);
      
      // Add subscription
      activeStompSubscriptions.add(roomId);
      expect(activeStompSubscriptions.contains(roomId), isTrue);
      
      // Try to add again (should not increase size)
      final sizeBefore = activeStompSubscriptions.length;
      activeStompSubscriptions.add(roomId);
      expect(activeStompSubscriptions.length, equals(sizeBefore));
      
      // Remove subscription
      activeStompSubscriptions.remove(roomId);
      expect(activeStompSubscriptions.contains(roomId), isFalse);
    });

    test('should handle duplicate message detection with timing', () {
      // Test timing-based duplicate detection
      const roomId = '123';
      const messageText = 'Hello World';
      
      // Messages sent in the same second should have same key
      final timestamp = DateTime.now().millisecondsSinceEpoch ~/ 1000;
      final key1 = '${roomId}_${messageText}_$timestamp';
      final key2 = '${roomId}_${messageText}_$timestamp';
      
      expect(key1, equals(key2));
      
      // Messages sent in different seconds should have different keys
      final futureTimestamp = timestamp + 1;
      final key3 = '${roomId}_${messageText}_$futureTimestamp';
      
      expect(key1, isNot(equals(key3)));
    });
  });
}
