# Miscellaneous
*.class
*.log
*.pyc
*.swp
.DS_Store
.atom/
.build/
.buildlog/
.history
.svn/
.swiftpm/
migrate_working_dir/

# IntelliJ related
*.iml
*.ipr
*.iws
.idea/

# The .vscode folder contains launch configuration and tasks you configure in
# VS Code which you may wish to be included in version control, so this line
# is commented out by default.
#.vscode/

# Flutter/Dart/Pub related
**/doc/api/
**/ios/Flutter/.last_build_id
.dart_tool/
.flutter-plugins
.flutter-plugins-dependencies
.pub-cache/
.pub/
/build/

# Symbolication related
app.*.symbols

# Obfuscation related
app.*.map.json

# Android Studio will place build artifacts here
/android/app/debug
/android/app/profile
/android/app/release

# Additional Flutter/Dart ignores
*.g.dart
*.freezed.dart
*.mocks.dart
*.config.dart

# Coverage
coverage/
lcov.info

# Local configuration files
android/local.properties
ios/Flutter/flutter_export_environment.sh

# Generated files
.flutter-plugins-dependencies
.packages
.metadata

# IDE files
.vscode/
*.code-workspace

# OS generated files
Thumbs.db
ehthumbs.db

# Temporary files
*.tmp
*.temp
*.bak

# Log files
*.log

# Documentation files that are auto-generated
*.md.bak

# Local history files
.lh/

# Generated Flutter files
**/Flutter/ephemeral/
**/Flutter/Generated.xcconfig
**/Flutter/flutter_export_environment.sh

# Android specific generated files
android/app/.cxx/
android/gradle-wrapper.jar
android/.gradle/
android/captures/
android/gradlew
android/gradlew.bat
android/local.properties
android/key.properties
android/**/*.keystore
android/**/*.jks

# iOS specific generated files
ios/Flutter/Generated.xcconfig
ios/Flutter/flutter_export_environment.sh
ios/Pods/
ios/.symlinks/
ios/Flutter/App.framework
ios/Flutter/Flutter.framework
ios/Flutter/Flutter.podspec
ios/ServiceDefinitions.json
ios/Runner/GeneratedPluginRegistrant.*

# macOS specific generated files
macos/Flutter/ephemeral/
macos/Flutter/Generated.xcconfig
macos/Flutter/flutter_export_environment.sh
