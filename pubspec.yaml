name: vector
description: "Vector - Modern Chat Application"
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev
# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1
environment:
  sdk: ^3.7.2
# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8
  # HTTP and WebSocket packages
  http: ^1.2.1
  http_parser: ^4.0.2
  dio: ^5.4.1
  web_socket_channel: ^2.4.0
  stomp_dart_client: ^2.0.0
  # Chat UI packages
  flutter_chat_ui: ^2.2.0
  flutter_chat_types: ^3.6.2
  flutter_chat_core: ^2.2.0
  # State management
  provider: ^6.1.4
  # Utilities
  image_picker: ^1.0.7
  cached_network_image: ^3.4.1
  uuid: ^4.3.3
  intl: ^0.19.0
  form_validator: ^2.1.1
  path_provider: ^2.1.2
  mime: ^1.0.5
  open_filex: ^4.3.4
  shared_preferences: ^2.2.2
  flutter_speed_dial: ^7.0.0
  equatable: ^2.0.7
  flutter_bloc: ^9.1.1
  bloc: ^9.0.0
  get_it: ^7.7.0
  google_fonts: ^6.1.0
  fl_chart: ^0.66.2
  path: ^1.8.3
  # Added missing dependencies for file viewing
  url_launcher: ^6.2.5
  video_player: ^2.8.2
  chewie: ^1.7.5
  flutter_pdfview: ^1.3.2
  file_picker: ^10.1.9
  photo_view: ^0.15.0
  shimmer: ^3.0.0
  flutter_staggered_grid_view: ^0.7.0
  # Local notifications for unread messages
  flutter_local_notifications: ^17.0.0
  # Permission handling
  permission_handler: ^11.3.1
  # App lifecycle management
  flutter_background_service: ^5.0.10
  # Device information
  device_info_plus: ^10.1.2
  connectivity_plus: ^6.1.4
dev_dependencies:
  flutter_test:
    sdk: flutter
  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0
  # Added for testing
  mockito: ^5.4.4
  build_runner: ^2.4.8
  # App icon generator
  flutter_launcher_icons: ^0.13.1
  # MSI installer generator
  msix: ^3.16.8
# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec
# The following section is specific to Flutter packages.
flutter:
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true
  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/
    - assets/images/chat_background.png
    - assets/icons/
  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images
  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package
  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package

# Flutter Launcher Icons configuration
flutter_launcher_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets/icons/icon.jpg"
  min_sdk_android: 21 # android min sdk min:16, default 21
  web:
    generate: true
    image_path: "assets/icons/icon.jpg"
    background_color: "#hexcode"
    theme_color: "#hexcode"
  windows:
    generate: true
    image_path: "assets/icons/icon.jpg"
    icon_size: 48 # min:48, max:256, default: 48

# MSIX configuration for Windows installer
msix_config:
  display_name: Vector Chat
  publisher_display_name: Vector Team
  identity_name: com.vector.chat
  msix_version: *******
  description: Vector - Modern Chat Application for Windows
  publisher: CN=Vector Team
  logo_path: assets/icons/icon.jpg
  start_menu_icon_path: assets/icons/icon.jpg
  tile_icon_path: assets/icons/icon.jpg
  icons_background_color: transparent
  architecture: x64
  capabilities: 'internetClient,microphone,webcam,picturesLibrary,videosLibrary,documentsLibrary'
  languages: en-us
  store: false
  install_certificate: false
